<template>
  <div class="call-page">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <text class="loading-text">{{ loadingText }}</text>
        <text class="loading-tip">正在连接通话...</text>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="hasError && !isLoading" class="error-container">
      <div class="error-content">
        <text class="error-text">{{ errorMessage }}</text>
        <div class="error-actions">
          <div class="retry-btn" @click="retryJoinChannel">
            <text class="retry-text">重试</text>
          </div>
          <div class="back-btn" @click="goBack">
            <text class="back-text">返回</text>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="!isLoading && !hasError" class="main-content">
      <!-- 视频区域 -->
      <div class="call-video-container">
        <!-- 全屏模式 -->
        <div v-if="isFullscreen" class="fullscreen-container">
          <!-- 返回按钮 -->
          <div class="fullscreen-back-btn" @click="exitFullscreen">
            <text class="back-btn-text">返回</text>
          </div>

          <!-- 全屏本地视频 -->
          <div
            v-if="fullscreenUserId === String(imOptions.account)"
            class="fullscreen-video"
          >
            <!-- #ifdef APP-PLUS -->
            <nertc-local-view
              class="video-view"
              mediaType="video"
              @onViewLoad="onLocalVideoViewLoad"
              :viewID="String(imOptions.account)"
            >
            </nertc-local-view>
            <!-- #endif -->
          </div>

          <!-- 全屏远程视频 -->
          <div v-else class="fullscreen-video">
            <!-- #ifdef APP-PLUS -->
            <nertc-remote-view
              class="video-view"
              v-if="fullscreenUserId"
              mediaType="video"
              @onViewLoad="onRemoteVideoViewLoad(fullscreenUserId)"
              :userID="fullscreenUserId"
            >
            </nertc-remote-view>
            <!-- #endif -->
          </div>
        </div>

        <!-- 正常视频网格 (包含远程视频和本地视频) -->
        <div
          v-else
          class="video-grid"
          :class="'participants-' + (remoteUserIdVideoList.length + 1)"
        >
          <!-- 本地视频 -->
          <div
            class="video-item"
            :style="{ width: videoItemWidth, height: videoItemHeight }"
            @click="enterFullscreen(String(imOptions.account))"
          >
            <div class="video-view-container">
              <!-- #ifdef APP-PLUS -->
              <nertc-local-view
                class="video-view"
                mediaType="video"
                @onViewLoad="onLocalVideoViewLoad"
                :viewID="String(imOptions.account)"
              >
              </nertc-local-view>
              <!-- #endif -->
            </div>
          </div>

          <!-- 远程视频列表 -->
          <div
            v-for="(userID, index) in remoteUserIdVideoList"
            :key="userID"
            class="video-item"
            :style="{ width: videoItemWidth, height: videoItemHeight }"
            @click="enterFullscreen(userID)"
          >
            <div class="video-view-container">
              <!-- #ifdef APP-PLUS -->
              <nertc-remote-view
                class="video-view"
                v-if="userID"
                mediaType="video"
                @onViewLoad="onRemoteVideoViewLoad(userID)"
                :userID="userID"
              >
              </nertc-remote-view>
              <!-- #endif -->
            </div>
          </div>
        </div>
      </div>

      <!-- 控制按钮区域 -->
      <div class="call-controls">
        <!-- 通话中的控制按钮 -->
        <div class="call-action-row">
          <div
            class="call-action-btn"
            :class="{ 'active-btn': isMuteAudio, 'disabled-btn': !isLogin }"
            @click="isLogin ? muteLocalAudio() : null"
          >
            <text class="btn-label">{{ isMuteAudio ? '取消静音' : '静音' }}</text>
          </div>

          <div
            class="call-action-btn"
            :class="{ 'active-btn': isSpeakerphoneOn, 'disabled-btn': !isLogin }"
            @click="isLogin ? setSpeakerphoneOn() : null"
          >
            <text class="btn-label">{{
              isSpeakerphoneOn ? '听筒' : '扬声器'
            }}</text>
          </div>

          <div
            class="call-action-btn"
            :class="{ 'disabled-btn': !isLogin }"
            @click="isLogin ? switchCamera() : null"
          >
            <text class="btn-label">切换摄像头</text>
          </div>

          <div
            class="call-action-btn"
            :class="{ 'active-btn': !isPublishingStream, 'disabled-btn': !isLogin }"
            @click="isLogin ? openTheCamera() : null"
          >
            <text class="btn-label">{{
              isPublishingStream ? '关闭视频' : '开启视频'
            }}</text>
          </div>



          <div
            class="call-action-btn hangup-btn"
            :class="{ 'disabled-btn': !isLogin }"
            @click="isLogin ? hangupAndReturn() : null"
          >
            <text class="btn-label">挂断</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getImToken, getPermissionKey } from '../../api/resource'
import { uploadCallScreenshot } from '../../api/index'
import Config from '../../utils/config'
// #ifdef APP-PLUS
import permision from '@/NERtcUniappSDK-JS/permission.js'
import NERTC from '@/NERtcUniappSDK-JS/lib/index'
import { pluginVersion } from '@/NERtcUniappSDK-JS/lib/index'
import NertcLocalView from '@/NERtcUniappSDK-JS/nertc-view/NertcLocalView'
import NertcRemoteView from '@/NERtcUniappSDK-JS/nertc-view/NertcRemoteView'
import {
  NERtcVideoStreamType,
  NERTCConnectionType,
} from '@/NERtcUniappSDK-JS/lib/NERtcDefines'
import keycenter from './keyCenter'
// #endif
export default {
  components: {
    NertcRemoteView: NertcRemoteView,
    NertcLocalView: NertcLocalView,
  },
  data() {
    return {
      engine: undefined,
      channelName: null,
      remoteUserIdVideoList: [],
      mediaType: 'video',
      isSetup: false, //是否初始化引擎
      isAutoSub: false, //自动订阅视频
      isLogin: false, //是否加入房间
      isSpeakerphoneOn: true, //默认扬声器
      isPublishingAudio: true, //默认发送音频
      isMuteAudio: false, //默认unmute
      isStopAudio: false, //默认发送二次
      isOpenAudio: true, //是否开启本地麦克风
      isPublishingStream: true, //是否开启本地摄像头
      isMuteVideo: false, //默认unmute
      isPreviewVideo: false, //预览本地视频
      isPreviewScreen: false, //预览本地视频
      // UI相关属性
      callStartTime: 0, // 通话开始时间
      callDuration: 0, // 通话时长（秒）
      timerInterval: null, // 计时器
      imOptions: uni.getStorageSync('__yx_im_options__'),
      channelId: null,
      callStatus: 'calling',
      callType: 2,
      // 屏幕尺寸
      screenHeight: 0,
      screenWidth: 0,
      // 全屏相关
      isFullscreen: false, // 是否全屏显示
      fullscreenUserId: null, // 当前全屏显示的用户ID
      // 加载状态相关
      isLoading: true, // 是否正在加载
      loadingText: '正在初始化...', // 加载文本
      hasError: false, // 是否有错误
      errorMessage: '', // 错误信息
      retryCount: 0, // 重试次数
      maxRetryCount: 3, // 最大重试次数
      joinChannelTimeout: null, // 加入房间超时定时器
      // 自动截图相关
      autoScreenshotInterval: null, // 自动截图定时器
      screenshotIntervalTime: 5000, // 截图间隔时间（5秒）
      // 截图上传相关
      isUploadEnabled: true, // 是否启用截图上传功能
      uploadQueue: [], // 上传队列
      isUploading: false, // 是否正在上传
      maxUploadRetries: 3, // 最大重试次数
      uploadTimeout: 30000, // 上传超时时间（30秒）
      maxQueueSize: 50, // 最大队列大小
    }
  },
  computed: {
    // 格式化通话时长
    formattedDuration() {
      const minutes = Math.floor(this.callDuration / 60)
      const seconds = this.callDuration % 60
      return `${minutes.toString().padStart(2, '0')}:${seconds
        .toString()
        .padStart(2, '0')}`
    },
    // 计算视频项目高度 - 屏幕高度的一半，并考虑底部工具栏的高度
    videoItemHeight() {
      // 工具栏高度约为140rpx（70px），我们减去这个高度再计算一半
      const availableHeight = this.screenHeight - 140 // 减去工具栏高度
      return availableHeight / 2 + 'px'
    },
    // 计算视频项目宽度 - 根据参与者数量调整
    videoItemWidth() {
      const totalParticipants = this.remoteUserIdVideoList.length + 1
      if (totalParticipants <= 2) {
        // 1-2人时宽度为屏幕宽度的80%
        return this.screenWidth * 0.45 + 'px'
      } else {
        // 3-4人时宽度为屏幕宽度的45%
        return this.screenWidth * 0.45 + 'px'
      }
    },
  },
  onShow() {
    console.log('onShow: 当 uni-app 启动，或从后台进入前台显示')
  },
  onHide() {
    console.log('onHide: 当 uni-app 从前台进入后台')
  },
  onReady() {
    console.log(
      'onReady: 监听页面初次渲染完成。注意如果渲染速度快，会在页面进入动画完成前触发'
    )
  },
  onInit(data) {
    console.log(
      'onInit: 监听页面初始化, 为上个页面传递的数据，触发时机早于 onLoad'
    )
  },
  async onLoad(data) {
    console.log('onLoad: 监听页面初始化, 为上个页面传递的数据')

    try {
      // 设置初始加载状态
      this.isLoading = true
      this.loadingText = '正在初始化...'
      this.hasError = false

      // 解析传入参数
      if (data.type) {
        this.callType = parseInt(data.type)
      }
      if (data.channelId) {
        this.channelId = data.channelId
      }
      if (data.status) {
        this.callStatus = data.status
      }

      // 使用channelId作为房间名
      this.channelName = this.channelId

      // 获取屏幕尺寸
      const systemInfo = uni.getSystemInfoSync()
      this.screenHeight = systemInfo.windowHeight
      this.screenWidth = systemInfo.windowWidth
      console.log('屏幕尺寸:', this.screenWidth, 'x', this.screenHeight)

      // 延迟一帧确保页面渲染完成
      await this.$nextTick()

      // 异步初始化通话
      await this.initializeCall()

    } catch (error) {
      console.error('页面初始化失败:', error)
      this.showError('页面初始化失败: ' + this.getUserFriendlyError(error))
    }
  },

  onUnload() {
    console.log('onUnload: 监听页面卸载')

    // 清理所有定时器
    if (this.joinChannelTimeout) {
      clearTimeout(this.joinChannelTimeout)
      this.joinChannelTimeout = null
    }

    // 停止自动截图
    this.stopAutoScreenshot()

    // 清理上传队列
    this.uploadQueue = []
    this.isUploading = false

    // 如果用户在通话中，先离开房间
    if (this.isLogin && this.engine) {
      try {
        this.leaveChannel()
      } catch (e) {
        console.error('离开房间失败:', e)
      }
    }
    try {
      this.destroyEngine()
    } catch (e) {
      console.error('销毁引擎失败:', e)
    }
  },
  onBackPress(event) {
    console.log(
      'onBackPress: 监听页面返回, 返回 event = {from:backbutton、 navigateBack} ，backbutton 表示来源是左上角返回按钮或 android 返回键；navigateBack表示来源是 uni.navigateBack '
    )

    // 如果当前是全屏模式，先退出全屏
    if (this.isFullscreen) {
      this.exitFullscreen()
      return true // 返回true，表示已处理返回操作
    }

    // 如果用户在通话中，先离开房间
    if (this.isLogin && this.engine) {
      try {
        this.leaveChannel()
      } catch (e) {
        console.error('离开房间失败:', e)
      }
    }
    try {
      this.destroyEngine()
    } catch (e) {
      console.error('销毁引擎失败:', e)
    }
    return false // 返回false，由系统处理返回操作
  },
  methods: {
    // 初始化通话流程
    async initializeCall() {
      try {
        // 检查必要参数
        if (!this.channelName) {
          throw new Error('缺少频道名称')
        }

        // 初始化引擎（如果还没有初始化）
        if (!this.isSetup) {
          this.loadingText = '正在初始化引擎...'
          this.createEngine()
        }

        // 设置超时处理
        this.setJoinChannelTimeout()

        // 异步加入频道
        await this.joinChannel()

      } catch (error) {
        console.error('初始化通话失败:', error)
        this.showError(this.getUserFriendlyError(error))
      }
    },

    // 设置加入房间超时
    setJoinChannelTimeout() {
      // 清除之前的超时定时器
      if (this.joinChannelTimeout) {
        clearTimeout(this.joinChannelTimeout)
      }

      console.log('设置30秒超时定时器')
      // 设置30秒超时
      this.joinChannelTimeout = setTimeout(() => {
        if (this.isLoading) {
          console.error('加入房间超时')
          this.showError('连接超时，请检查网络后重试')
        }
      }, 30000)
    },

    // 显示错误状态
    showError(message) {
      this.isLoading = false
      this.hasError = true
      this.errorMessage = message

      // 清除超时定时器
      if (this.joinChannelTimeout) {
        clearTimeout(this.joinChannelTimeout)
        this.joinChannelTimeout = null
      }
    },

    // 重试加入频道
    async retryJoinChannel() {
      if (this.retryCount >= this.maxRetryCount) {
        this.showError('重试次数已达上限，请稍后再试')
        return
      }

      // 检查网络状态
      const hasNetwork = await this.checkNetworkStatus()
      if (!hasNetwork) {
        this.showError('网络连接不可用，请检查网络设置')
        return
      }

      console.log('开始重试，当前重试次数:', this.retryCount)

      this.retryCount++
      this.isLoading = true
      this.hasError = false
      this.loadingText = `正在重试连接... (${this.retryCount}/${this.maxRetryCount})`

      try {
        // 清理之前的状态
        await this.cleanupBeforeRetry()

        // 设置超时处理
        this.setJoinChannelTimeout()

        // 重新加入频道（不重新初始化引擎）
        await this.joinChannel()

      } catch (error) {
        console.error('重试失败:', error)
        this.showError('重试失败: ' + this.getUserFriendlyError(error))
      }
    },

    // 重试前清理状态
    async cleanupBeforeRetry() {
      console.log('重试前清理状态')

      // 如果已经在房间中，先离开
      if (this.isLogin && this.engine) {
        try {
          console.log('重试前先离开房间')
          this.engine.leaveChannel()
          this.isLogin = false
        } catch (e) {
          console.error('离开房间失败:', e)
        }
      }

      // 停止计时器
      this.stopCallTimer()

      // 停止自动截图
      this.stopAutoScreenshot()

      // 清除超时定时器
      if (this.joinChannelTimeout) {
        clearTimeout(this.joinChannelTimeout)
        this.joinChannelTimeout = null
      }

      // 重置部分状态（但不重置引擎）
      this.remoteUserIdVideoList = []
      this.isFullscreen = false
      this.fullscreenUserId = null

      // 短暂延迟确保清理完成
      await new Promise(resolve => setTimeout(resolve, 500))
    },
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    // 检查网络状态
    checkNetworkStatus() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: (res) => {
            if (res.networkType === 'none') {
              resolve(false)
            } else {
              resolve(true)
            }
          },
          fail: () => {
            // 获取网络状态失败，假设有网络
            resolve(true)
          }
        })
      })
    },

    // 安全获取错误信息
    getErrorMessage(error) {
      if (!error) return '未知错误'

      // 如果是字符串，直接返回
      if (typeof error === 'string') return error

      // 如果有 message 属性
      if (error.message) return error.message

      // 如果有 msg 属性（某些 API 返回格式）
      if (error.msg) return error.msg

      // 如果有 errMsg 属性（uni-app 错误格式）
      if (error.errMsg) return error.errMsg

      // 尝试转换为字符串
      try {
        return error.toString()
      } catch (e) {
        return '未知错误'
      }
    },

    // 获取用户友好的错误信息
    getUserFriendlyError(error) {
      const errorMessage = this.getErrorMessage(error)

      if (errorMessage.includes('网络') || errorMessage.includes('network')) {
        return '网络连接失败，请检查网络后重试'
      } else if (errorMessage.includes('token') || errorMessage.includes('Token')) {
        return '获取通话凭证失败，请稍后重试'
      } else if (errorMessage.includes('权限') || errorMessage.includes('permission')) {
        return '权限验证失败，请重新登录'
      } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        return '连接超时，请检查网络后重试'
      } else if (errorMessage.includes('频道') || errorMessage.includes('channel')) {
        return '频道信息错误，请重新进入'
      } else {
        return errorMessage
      }
    },

    clear() {
      this.remoteUserIdVideoList = [] //房间中远端视频list
      this.isLogin = false //是否加入房间
      this.isSpeakerphoneOn = true //默认扬声器
      this.isPublishingAudio = true //默认发送音频
      this.isMuteAudio = false //默认unmute
      this.isStopAudio = false //默认发送
      this.isOpenAudio = true //是否开启本地麦克风
      this.isPublishingStream = true //是否开启本地摄像头
      this.isMuteVideo = false //默认unmute
      this.isPreviewVideo = false //预览本地视频
      this.isPreviewScreen = false //预览本地视频
      // 重置UI相关状态
      this.stopCallTimer()
      // 停止自动截图
      this.stopAutoScreenshot()
      // 清理上传队列
      this.uploadQueue = []
      this.isUploading = false
      // 重置全屏状态
      this.isFullscreen = false
      this.fullscreenUserId = null
      // 重置加载状态
      this.isLoading = false
      this.hasError = false
      this.retryCount = 0
      // 清除超时定时器
      if (this.joinChannelTimeout) {
        clearTimeout(this.joinChannelTimeout)
        this.joinChannelTimeout = null
      }
    },

    // 进入全屏模式
    enterFullscreen(userId) {
      console.log('进入全屏模式，用户ID:', userId)
      if (this.engine) {
        try {
          this.engine.nertcPrint('进入全屏模式，用户ID: ' + userId)
        } catch (e) {
          console.error('nertcPrint调用失败:', e)
        }
      }

      this.fullscreenUserId = userId
      this.isFullscreen = true

      // 如果是远程用户，确保已订阅
      if (userId !== String(this.imOptions.account)) {
        const userIdNum = parseInt(userId)
        if (!isNaN(userIdNum)) {
          this.subscribeRemoteVideo(userIdNum, userId)
        }
      }
    },

    // 退出全屏模式
    exitFullscreen() {
      console.log('退出全屏模式')
      if (this.engine) {
        try {
          this.engine.nertcPrint('退出全屏模式')
        } catch (e) {
          console.error('nertcPrint调用失败:', e)
        }
      }

      this.isFullscreen = false
      this.fullscreenUserId = null
    },



    // 开始计时器
    startCallTimer() {
      this.callStartTime = Math.floor(Date.now() / 1000)
      this.timerInterval = setInterval(() => {
        const now = Math.floor(Date.now() / 1000)
        this.callDuration = now - this.callStartTime
      }, 1000)
    },

    // 停止计时器
    stopCallTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval)
        this.timerInterval = null
      }
      this.callDuration = 0
    },

    // 开始自动本地截图
    startAutoScreenshot() {
      if (!this.engine) {
        return
      }

      console.log('开始自动本地截图，间隔时间:', this.screenshotIntervalTime / 1000, '秒')
      this.engine.nertcPrint('开始自动本地截图，间隔时间: ' + (this.screenshotIntervalTime / 1000) + '秒')

      // 清除之前的定时器
      this.stopAutoScreenshot()

      // 设置定时器，每5秒执行一次本地截图
      this.autoScreenshotInterval = setInterval(() => {
        this.takeAllUsersSnapshot()
      }, this.screenshotIntervalTime)
    },

    // 停止自动截图
    stopAutoScreenshot() {
      if (this.autoScreenshotInterval) {
        console.log('停止自动截图')
        if (this.engine) {
          this.engine.nertcPrint('停止自动截图')
        }
        clearInterval(this.autoScreenshotInterval)
        this.autoScreenshotInterval = null
      }
    },

    // 添加截图到上传队列
    addToUploadQueue(screenshotData) {
      // 检查队列大小，避免内存溢出
      if (this.uploadQueue.length >= this.maxQueueSize) {
        console.warn('上传队列已满，丢弃最旧的截图')
        this.uploadQueue.shift() // 移除最旧的截图
      }

      this.uploadQueue.push({
        ...screenshotData,
        timestamp: Date.now(),
        retryCount: 0,
        id: Date.now() + Math.random() // 生成唯一ID
      })

      console.log(`截图已添加到上传队列，当前队列长度: ${this.uploadQueue.length}`)

      // 触发上传处理
      this.processUploadQueue()
    },

    // 处理上传队列
    async processUploadQueue() {
      if (this.isUploading || this.uploadQueue.length === 0) {
        return
      }

      this.isUploading = true

      while (this.uploadQueue.length > 0) {
        const screenshotData = this.uploadQueue.shift()

        try {
          await this.uploadScreenshotToServer(screenshotData)
          console.log('截图上传成功:', screenshotData.id)
        } catch (error) {
          console.error('截图上传失败:', error)

          // 重试逻辑
          if (screenshotData.retryCount < this.maxUploadRetries) {
            screenshotData.retryCount++
            console.log(`截图上传失败，准备重试 (${screenshotData.retryCount}/${this.maxUploadRetries}):`, screenshotData.id)

            // 重新添加到队列末尾
            this.uploadQueue.push(screenshotData)
          } else {
            console.error('截图上传失败，已达最大重试次数:', screenshotData.id)
          }
        }

        // 添加小延迟，避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      this.isUploading = false
    },

    // 上传截图到服务器
    async uploadScreenshotToServer(screenshotData) {
      try {
        console.log('开始上传截图到服务器:', screenshotData.id)

        // 准备上传数据
        const uploadData = {
          channelId: this.channelId, // 通话频道ID
          accid: screenshotData.accid, // 用户ID
          userType: screenshotData.userType, // 用户类型：local/remote
          imageData: screenshotData.base64Data, // base64图片数据
          captureTime: screenshotData.captureTime, // 截图时间
          callStartTime: this.callStartTime * 1000, // 通话开始时间（转换为毫秒）
          imageFormat: 'png', // 图片格式
          imageSize: screenshotData.imageSize || 0, // 图片大小
          deviceInfo: {
            platform: uni.getSystemInfoSync().platform,
            version: uni.getSystemInfoSync().version,
            model: uni.getSystemInfoSync().model
          }
        }

        // 调用上传API
        const response = await uploadCallScreenshot(uploadData)

        if (response && response.code === 200) {
          console.log('截图上传成功:', response)
          return response
        } else {
          throw new Error(`上传失败: ${response?.msg || '未知错误'}`)
        }
      } catch (error) {
        console.error('上传截图到服务器失败:', error)
        throw error
      }
    },

    // 进行本地截图
    takeAllUsersSnapshot() {
      if (!this.engine || !this.isLogin) {
        return
      }

      console.log('开始本地截图')
      this.engine.nertcPrint('开始本地截图')

      // 只截取本地视频
      this.takeLocalSnapshot()
    },
    createEngine() {
      if (this.isSetup) {
        this.destroyEngine()
        this.isSetup = false
        return
      }
      console.log('初始化NERTC引擎, SDK版本: ', pluginVersion)
      this.engine = NERTC.setupEngineWithContext({
        appKey: '9471dc5329b529d957bc475d6b531be2',
        logDir: '', // expected log directory
        logLevel: 3,
      })
      //先监听SDK的重要事件
      this.addEventListener()
      console.log('初始化引擎完成，开始设置本地视频画布')
      this.engine.nertcPrint('初始化引擎完成，开始设置本地视频画布')
      // #ifdef APP-PLUS
      // 由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，因此该组件onViewLoad事件触发是，还没有初始化音视频SDK的引擎，所以需要再初始化引擎的时候设置一下视频画布
      this.engine.setupLocalVideoCanvas(keycenter.getLocalVideoCanvasConfig())
      // #endif
      console.log('初始化引擎完成，enableLocalVideo')
      this.engine.nertcPrint('初始化引擎完成，enableLocalVideo')

      // #ifdef APP-PLUS
      //判断权限
      if (uni.getSystemInfoSync().platform === 'android') {
        permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
        permision.requestAndroidPermission('android.permission.CAMERA')
      }
      // #endif

      this.engine.enableLocalVideo({
        enable: this.isPublishingStream, //true表示设置启动摄像头，false表示关闭摄像头
        videoStreamType: 0, //0表示视频，1表示屏幕共享 //当前demo先使用数字，正式版本会是枚举
      })

      //设置视频编码属性
      this.engine.setLocalVideoConfig(keycenter.getVideoConfig())
      this.isSetup = true
    },
    // 创建一个安全的事件处理包装器
    createSafeEventHandler(callback) {
      return (...args) => {
        try {
          if (this.engine) {
            callback.apply(this, args)
          } else {
            console.log('引擎已销毁，忽略事件回调')
          }
        } catch (e) {
          console.error('事件处理出错:', e)
        }
      }
    },

    addEventListener() {
      // 使用安全的事件处理包装器
      this.engine.addEventListener(
        'onsetupEngineWithContext',
        this.createSafeEventHandler((message) => {
          const imessage = `onsetupEngineWithContext通知：message = ${message}`
          this.engine.nertcPrint(imessage)
          console.log(imessage)
        })
      )

      //注册NERTC的事件
      this.engine.addEventListener(
        'onError',
        this.createSafeEventHandler((code, message, extraInfo) => {
          let imessage = `onError通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
          this.engine.nertcPrint(imessage)
          console.log(imessage)

          // 显示用户友好的错误信息
          let userMessage = '通话出现错误'
          if (code === 30001) {
            userMessage = '网络连接失败，请检查网络'
          } else if (code === 30003) {
            userMessage = '服务器连接超时'
          } else if (code === 30004) {
            userMessage = '权限验证失败'
          } else {
            userMessage = `通话错误 (${code})`
          }

          this.showError(userMessage)
        })
      )
      this.engine.addEventListener(
        'onWaring',
        this.createSafeEventHandler((code, message, extraInfo) => {
          const imessage = `onWaring通知：code = ${code}, message = ${message}, extraInfo = ${extraInfo}`
          this.engine.nertcPrint(imessage)
          console.log(imessage)
          uni.showToast({
            title: imessage,
            icon: 'none',
            duration: 3000,
          })
        })
      )
      this.engine.addEventListener(
        'onServiceEnable',
        this.createSafeEventHandler((serverType, isEnable) => {
          const imessage = `onServiceEnable通知：serverType = ${serverType}, isEnable = ${isEnable}`
          this.engine.nertcPrint(imessage)
          console.log(imessage)
          uni.showToast({
            title: imessage,
            icon: 'none',
            duration: 3000,
          })
        })
      )
      this.engine.addEventListener(
        'onJoinChannel',
        this.createSafeEventHandler(
          (result, channelId, elapsed, userID, userStringID) => {
            let message = `onJoinChannel通知：自己加入房间状况，result = ${result}, channelId = ${channelId}, elapsed = ${elapsed}, userID = ${userID}, userStringID = ${userStringID}`
            this.engine.nertcPrint(message)
            console.log(message)

            // 处理加载状态
            if (result === 0) {
              // 加入成功
              console.log('onJoinChannel: 成功加入房间')
              this.isLoading = false
              this.hasError = false
              this.retryCount = 0 // 重置重试次数
              this.isLogin = true // 设置已登录状态

              // 开始计时
              this.startCallTimer()

              // 开始自动本地截图
              this.startAutoScreenshot()

              // 清除超时定时器
              if (this.joinChannelTimeout) {
                clearTimeout(this.joinChannelTimeout)
                this.joinChannelTimeout = null
              }

              uni.showToast({
                title: '已加入通话',
                icon: 'success',
                duration: 1500,
              })
            } else {
              // 加入失败
              console.log('onJoinChannel: 加入房间失败，错误码:', result)
              this.showError(`加入房间失败，错误码: ${result}`)
              return
            }

            if (!this.isPublishingStream)
              this.isPublishingStream = !this.isPublishingStream
            //加入房间后设置音频profile
            this.engine.setAudioProfile(keycenter.getAudioProfileConfig())
            const connectState = this.engine.getConnectionState()

            //设置采集音量
            const volume =
              keycenter?.getAudioVolumeConfig().captureVolume || 100
            this.engine.adjustRecordingSignalVolume(volume)

            //设置整个房间的播放音量
            const channelPlaybackVolume =
              keycenter.getAudioVolumeConfig().channelPlaybackVolume
            this.engine.adjustChannelPlaybackSignalVolume(channelPlaybackVolume)
            //获取链接状态
            message = `getConnectionState：获取链接状态，connectState = ${connectState}`
            this.engine.nertcPrint(message)
            console.log(message)
          }
        )
      )

      this.engine.addEventListener(
        'onReconnectingStart',
        this.createSafeEventHandler((result) => {
          const message = `onReconnectingStart通知：开始重连`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: message,
            icon: 'none',
            duration: 3000,
          })
        })
      )
      this.engine.addEventListener(
        'onReJoinChannel',
        this.createSafeEventHandler((result) => {
          const message = `onReJoinChannel通知：自己重新加入房间状况，result = ${result}`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: 'onReJoinChannel结果: ' + result,
            icon: 'none',
            duration: 3000,
          })
        })
      )
      this.engine.addEventListener(
        'onDisconnect',
        this.createSafeEventHandler((reason) => {
          const message = `onDisconnect通知：断开reason = ${reason}`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: 'onDisconnect原因: ' + reason, // 修正了这里的变量名
            icon: 'none',
            duration: 2000,
          })
        })
      )
      this.engine.addEventListener(
        'onConnectionTypeChanged',
        this.createSafeEventHandler((newConnectionType) => {
          const message = `onConnectionTypeChanged通知：newConnectionType=${newConnectionType}`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: '网络类型变化为: ' + NERTCConnectionType[newConnectionType],
            icon: 'none',
            duration: 3000,
          })
        })
      )
      this.engine.addEventListener(
        'onConnectionStateChanged',
        this.createSafeEventHandler((state, reason) => {
          const message = `onConnectionStateChanged通知：state=${state}, reason = ${reason}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onLeaveChannel',
        this.createSafeEventHandler((result) => {
          const message = `onLeaveChannel通知：自己离开房间状况，result = ${result}`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: '离开房间结果: ' + result,
            icon: 'none',
          })
        })
      )

      this.engine.addEventListener(
        'onUserJoined',
        this.createSafeEventHandler((userID, customInfo, userStringID) => {
          const message = `onUserJoined通知：有人加入房间，userID = ${userID}, userStringID = ${userStringID}, customInfo = ${customInfo}`
          this.engine.nertcPrint(message)
          console.log(message)
          uni.showToast({
            title: `${userID}加入房间`,
            icon: 'none',
          })
        })
      )

      this.engine.addEventListener(
        'onUserLeave',
        this.createSafeEventHandler(
          (userID, reason, customInfo, userStringID) => {
            const message = `onUserLeaved通知：有人离开房间，userID = ${userID}, userStringID = ${userStringID}, reason = ${reason}, customInfo = ${customInfo}`
            this.engine.nertcPrint(message)
            console.log(message)
            //建议在当前页面上销毁 nertc-remote-view组件
            const remoteUserID = userStringID
            let list = this.remoteUserIdVideoList.filter(
              (val) => val !== remoteUserID
            )
            this.remoteUserIdVideoList = list
            uni.showToast({
              title: `${userStringID}离开房间`,
              icon: 'none',
            })
          }
        )
      )

      this.engine.addEventListener(
        'onUserAudioStart',
        this.createSafeEventHandler((userID, userStringID) => {
          //sdk自动订阅对端音频，这里仅仅是通知信息，开发者不需要做什么逻辑
          const message = `onUserAudioStart通知：对方开启音频，userID = ${userID}, userStringID = ${userStringID}`
          this.engine.nertcPrint(message)
          console.log(message)

          //设置播放音量
          const volume = keycenter.getAudioVolumeConfig().userPlaybackVolume
          this.engine.adjustUserPlaybackSignalVolume({
            volume,
            userID,
          })
        })
      )

      this.engine.addEventListener(
        'onUserAudioStop',
        this.createSafeEventHandler((userID, userStringID) => {
          const message = `onUserAudioStop通知：对方关闭音频，userID = ${userID}, userStringID = ${userStringID}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )

      this.engine.addEventListener(
        'onUserVideoStart',
        this.createSafeEventHandler((userID, maxProfile, userStringID) => {
          const message = `onUserVideoStart通知：对方开启视频，userID = ${userID}, userStringID = ${userStringID}, maxProfile = ${maxProfile}`
          this.engine.nertcPrint(message)
          console.log(message)
          //nertc-remote-view组件 viewID和userID的数据格式是String类型，onUserVideoStart事件通知的userID是number，这里做一下数据格式转换
          const remoteUserID = userStringID //`${userID}`
          if (!this.remoteUserIdVideoList.includes(remoteUserID)) {
            this.remoteUserIdVideoList.push(remoteUserID)
            //保证当前nertc-remote-view组件渲染完成，在执行设置画布的接口
            this.$nextTick(() => {
              if (this.engine) {
                // 确保engine仍然存在
                console.warn('此时远端视频 nertc-remote-view 渲染完成')
                this.engine.nertcPrint(
                  '此时远端视频 nertc-remote-view 渲染完成'
                )
                //需要开发者主动去做订阅对方视频的逻辑动作
                this.subscribeRemoteVideo(userID, userStringID)
              }
            })
          }
        })
      )

      this.engine.addEventListener(
        'onUserVideoStop',
        this.createSafeEventHandler((userID, userStringID) => {
          const message = `onUserVideoStop通知：对方关闭视频，userID = ${userID}, userStringID = ${userStringID}`
          this.engine.nertcPrint(message)
          console.log(message)
          //建议在当前页面上销毁对应的nertc-remote-view组件
          const remoteUserID = userStringID //`${userID}`
          //对端关闭视频，建议主动调用接口释放对端视频画布相关的资源
          this.engine.destroyRemoteVideoCanvas({
            userID,
            userStringID,
          })
          const list = this.remoteUserIdVideoList.filter(
            (val) => val !== remoteUserID
          )
          this.remoteUserIdVideoList = list
        })
      )

      this.engine.addEventListener(
        'onUserAudioMute',
        this.createSafeEventHandler((userID, eanble, userStringID) => {
          const message = `onUserAudioMute通知：对方mute音频，userID = ${userID}, userStringID = ${userStringID}, eanble = ${eanble}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )

      this.engine.addEventListener(
        'onUserVideoMute',
        this.createSafeEventHandler(
          (userID, eanble, videoStreamType, userStringID) => {
            const message = `onUserVideoMute通知：对方mute视频，userID = ${userID}, userStringID = ${userStringID}, eanble = ${eanble}`
            this.engine.nertcPrint(message)
            console.log(message)
          }
        )
      )

      this.engine.addEventListener(
        'onAudioDeviceChanged',
        this.createSafeEventHandler((selected) => {
          const message = `onAudioDeviceChanged通知：对方mute视频，selected = ${selected}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )

      this.engine.addEventListener(
        'onAudioDeviceStateChange',
        this.createSafeEventHandler((deviceType, deviceState) => {
          const message = `onAudioDeviceStateChange通知：对方mute视频，deviceType = ${deviceType}, deviceState = ${deviceState}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onFirstAudioDataReceived',
        this.createSafeEventHandler((userID, userStringID) => {
          const message = `onFirstAudioDataReceived通知：userID = ${userID}, userStringID = ${userStringID}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onFirstVideoDataReceived',
        this.createSafeEventHandler((userID, videoStreamType, userStringID) => {
          const message = `onFirstVideoDataReceived通知：userID = ${userID}, userStringID = ${userStringID}, videoStreamType = ${videoStreamType}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onFirstAudioFrameDecoded',
        this.createSafeEventHandler((userID, userStringID) => {
          const message = `onFirstAudioFrameDecoded通知：userID = ${userID}, userStringID = ${userStringID}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onFirstVideoFrameDecoded',
        this.createSafeEventHandler((userID, videoStreamType, userStringID) => {
          const message = `onFirstVideoFrameDecoded通知：userID = ${userID}, userStringID = ${userStringID}, videoStreamType = ${videoStreamType}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onLocalAudioVolumeIndication',
        this.createSafeEventHandler((volume, vadFlag) => {
          const message = `onLocalAudioVolumeIndication通知：本地音量volume = ${volume}, vadFlag = ${vadFlag}`
          this.engine.nertcPrint(message)
          console.log(message)
        })
      )
      this.engine.addEventListener(
        'onRemoteAudioVolumeIndication',
        this.createSafeEventHandler((volumeList, totalVolume) => {
          const message = `onRemoteAudioVolumeIndication通知：总音量totalVolume = ${totalVolume}`
          this.engine.nertcPrint(message)
          console.log(message)
          volumeList.forEach((user) => {
            console.log(
              `用户 userID=${user.userID}, userStringID=${user.userStringID} 的音频音量为: ${user.volume},音频辅流的音量为: ${user.subStreamVolume} `
            )
            this.engine.nertcPrint(
              `用户 userID=${user.userID}, userStringID=${user.userStringID} 的音频音量为: ${user.volume},音频辅流的音量为: ${user.subStreamVolume} `
            )
          })
        })
      )
      this.engine.addEventListener(
        'onNetworkQuality',
        this.createSafeEventHandler((statsArray) => {
          statsArray.forEach((user) => {
            console.log(
              `用户 userID=${user.userID}, userStringID=${user.userStringID} 的上行网络质量为: ${user.upStatus}, 下行网络质量为: ${user.downStatus} `
            )
            this.engine.nertcPrint(
              `用户 userID=${user.userID}, userStringID=${user.userStringID} 的上行网络质量为: ${user.upStatus}, 下行网络质量为: ${user.downStatus} `
            )
          })
        })
      )
      this.engine.addEventListener(
        'onRtcStats',
        this.createSafeEventHandler((rtcStats) => {
          const message = `onRtcStats通知: rtcStats= ${JSON.stringify(
            rtcStats
          )} `
          this.engine.nertcPrint(message)
        })
      )
      this.engine.addEventListener(
        'onLocalAudioStats',
        this.createSafeEventHandler((stats) => {
          stats.audioLayers.forEach((item) => {
            const message = `onLocalAudioStats 本地音频流统计信息: ${JSON.stringify(
              item
            )}`
            this.engine.nertcPrint(message)
            console.log(message)
          })
        })
      )
      this.engine.addEventListener(
        'onRemoteAudioStats',
        this.createSafeEventHandler((stats) => {
          this.engine.nertcPrint(
            'onRemoteAudioStats通知 长度length ' + stats.length
          )
          stats.forEach((item) => {
            const message = `onRemoteAudioStats 用户 userID=${item.userID}, userStringID=${item.userStringID} 的音频统计`
            this.engine.nertcPrint(message)
            console.log(message)
            item.audioLayers.forEach((info) => {
              const imessage = `onRemoteAudioStats 用户 userID=${
                item.userID
              }, userStringID=${
                item.userStringID
              } 的音频统计信息: ${JSON.stringify(info)} `
              this.engine.nertcPrint(imessage)
              console.log(imessage)
            })
          })
        })
      )
      this.engine.addEventListener(
        'onLocalVideoStats',
        this.createSafeEventHandler((stats) => {
          stats.videoLayers.forEach((item) => {
            const message = `onLocalVideoStats 本地视频流统计信息: ${JSON.stringify(
              item
            )}`
            this.engine.nertcPrint(message)
            console.log(message)
          })
        })
      )
      this.engine.addEventListener(
        'onRemoteVideoStats',
        this.createSafeEventHandler((stats) => {
          this.engine.nertcPrint(
            'onRemoteVideoStats通知 长度length ' + stats.length
          )
          stats.forEach((item) => {
            const message = `onRemoteVideoStats 用户 userID=${item.userID}, userStringID=${item.userStringID} 的视频统计`
            this.engine.nertcPrint(message)
            console.log(message)
            item.videoLayers.forEach((info) => {
              const imessage = `onRemoteVideoStats 用户 userID=${
                item.userID
              }, userStringID=${
                item.userStringID
              } 的视频统计信息: ${JSON.stringify(info)} `
              this.engine.nertcPrint(imessage)
              console.log(imessage)
            })
          })
        })
      )
    },
    destroyEngine() {
      console.log('销毁引擎')
      if (this.engine) {
        try {
          this.engine.nertcPrint('销毁引擎')
          this.engine.removeAllEventListener()
          this.engine.destroyEngine()
        } catch (e) {
          console.error('销毁引擎过程中出错:', e)
        } finally {
          this.engine = null
        }
      }
      this.clear()
      this.isSetup = false //是否初始化引擎
      this.isAutoSub = false
    },

    onLocalVideoViewLoad() {
      const message = '本端视频预览的view组件加载完成，可以设置画布'
      console.log(message)
      if (!this.engine) {
        //由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，此时应该还没有初始化音视频SDK的引擎
        return
      }
      this.engine.nertcPrint(message)
      this.engine.setupLocalVideoCanvas(keycenter.getLocalVideoCanvasConfig())
    },
    onLocalSubVideoLoadView() {
      const message =
        '本端视频辅流预览的nertc-local-view组件加载完成，可以设置画布'
      console.log(message)
      this.engine.nertcPrint(message)
      this.engine.setupLocalSubStreamVideoCanvas(
        Object.assign(keycenter.getLocalSubStreamVideoCanvasConfig())
      )
    },
    onRemoteVideoViewLoad(userID) {
      if (!this.engine) {
        //由于当前Demo本地视频摄像头预览的nertc-local-view组件初始化就加载了，此时应该还没有初始化音视频SDK的引擎
        return
      }
      const message =
        '远端视频预览的view组件加载完成，可以设置画布, userID: ' + userID
      console.log(message)
      this.engine.nertcPrint(message)
      //当前Demo使用，nertc-remote-view对应的UserID和viewID，都是userStringID，所以这里的参数使用userStringID
      this.engine.setupRemoteVideoCanvas(keycenter.getRemoteVideoCanvasConfig())
    },
    onRemoteSubVideoViewLoad(userID) {
      const message =
        '远端视频辅流预览的view组件加载完成，可以设置画布, userID: ' + userID
      console.log(message)
      this.engine.nertcPrint(message)
      //当前Demo使用，nertc-remote-view对应的UserID和viewID，都是userStringID，所以这里的参数使用userStringID
      const config = Object.assign(
        keycenter.getRemoteSubStreamVideoCanvasConfig(),
        {
          userStringID: userID,
        }
      )
      this.engine.setupRemoteSubStreamVideoCanvas(config)
    },
    enbaleAutoSub() {
      let message = '设置自动订阅视频: ' + this.isAutoSub
      console.log(message)
      this.engine.nertcPrint(message)
      this.isAutoSub = !this.isAutoSub
      let params = {
        internal: true, //内部标识
        video_prefer_hw_encoder: false,
        video_prefer_hw_decoder: false,
        video_start_with_back_camera: false,
        video_sendonpub_type: 0,
        auto_subscribe_audio: this.isAutoSub,
        record_host_enabled: false,
        record_audio_enabled: false,
        record_video_enabled: false,
        record_type: 0,
        publish_self_stream_enabled: true,
        audio_processing_aec_enable: true,
        audio_processing_agc_enable: true,
        audio_processing_ns_enable: true,
        audio_processing_external_audiomix_enable: false,
        audio_processing_earphone: false,
        media_url: '',
        quic_url: '',
        test_1v1: false,
        is_debug_server: false,
        key_test_server_uri: false,
        auto_subscribe_video: true,
        drop_bandwidth_enabled: false,
        enable_encrypting_and_compressing_log: false,
      }
      this.engine.setParameters(params)
    },
    isServiceEnable() {
      let message = '是否开启了前台服务'
      console.log(message)
      this.engine.nertcPrint(message)
      this.engine.isServiceEnable('FOREGROUND_SERVICE')
    },
    async joinChannel() {
      try {
        // 检查是否已经在房间中
        if (this.isLogin) {
          this.leaveChannel()
          this.isLogin = false
          return
        }

        // 确保引擎已初始化
        if (!this.isSetup || !this.engine) {
          throw new Error('引擎未初始化')
        }

        this.loadingText = '正在获取通话凭证...'
        console.log('获取token')

        // 并行获取token和权限密钥以提高效率
        const [tokenRes, keyRes] = await Promise.all([
          getImToken({
            channelName: this.channelName,
            ttlSec: '3600000', // 设置token有效期为1小时
          }),
          getPermissionKey({
            channelName: this.channelName,
            permSecret: Config.PERM_SECRET, // 使用配置文件中的权限密钥
            ttlSec: '3600000', // 设置权限密钥有效期为1小时
            privilege: '63', // 默认权限值
          })
        ])

        console.log('Token响应:', tokenRes)
        console.log('权限密钥响应:', keyRes)

        // 检查token获取结果
        if (tokenRes.code !== 200 || !tokenRes.msg) {
          throw new Error('获取通话Token失败: ' + (tokenRes.message || '未知错误'))
        }

        // 检查权限密钥获取结果
        if (keyRes.code !== 200 || !keyRes.msg) {
          throw new Error('获取权限密钥失败: ' + (keyRes.message || '未知错误'))
        }

        // 保存token和密钥
        const rtcToken = tokenRes.msg
        const permissionKey = keyRes.msg

        this.loadingText = '正在加入房间...'

        const joinInfo = {
          token: rtcToken,
          channelName: this.channelName,
          myUid: parseInt(this.imOptions.account),
          myStringUid: this.imOptions.account,
          permissionKey: permissionKey,
        }

        console.log('加入房间信息:', joinInfo)

        if (this.engine) {
          this.engine.nertcPrint('加入房间')
          this.engine.joinChannel(joinInfo)
        } else {
          throw new Error('引擎未初始化')
        }

        // 注意：不在这里设置 isLogin = true 和其他状态
        // 这些状态将在 onJoinChannel 事件回调中处理
        console.log('joinChannel 调用完成，等待 onJoinChannel 事件确认')

      } catch (error) {
        console.error('加入房间失败:', error)
        throw error // 重新抛出错误，让上层处理
      }
    },
    leaveChannel() {
      console.log('离开房间')
      if (this.engine) {
        try {
          this.engine.nertcPrint('离开房间')
          this.engine.leaveChannel()
        } catch (e) {
          console.error('离开房间过程中出错:', e)
        }
      }

      // 停止计时
      this.stopCallTimer()
      // 停止自动截图
      this.stopAutoScreenshot()
      this.clear()

      uni.showToast({
        title: '已离开房间',
        icon: 'none',
      })
    },

    // 挂断并返回聊天界面
    hangupAndReturn() {
      console.log('挂断并返回聊天界面')
      // 安全地调用nertcPrint，确保engine存在
      if (this.engine) {
        try {
          this.engine.nertcPrint('挂断并返回聊天界面')
        } catch (e) {
          console.error('nertcPrint调用失败:', e)
        }
      }

      // 先离开房间
      if (this.isLogin) {
        this.leaveChannel()
      }

      // 然后返回上一页（聊天界面）
      setTimeout(() => {
        uni.navigateBack({
          delta: 1,
          success: () => {
            console.log('成功返回聊天界面')
          },
          fail: (err) => {
            console.error('返回聊天界面失败:', err)
          },
        })
      }, 300) // 短暂延迟确保离开房间操作完成
    },
    getConnectionState() {
      console.log('点击 getConnectionState')
      this.engine.nertcPrint('点击 getConnectionState')
      const state = this.engine.getConnectionState()
      uni.showToast({
        title: 'ConnectionState: ' + state,
        icon: 'none',
        duration: 3000,
      })
    },
    enableMediaPub() {
      let message = 'enableMediaPub 是否停止发送语音: ' + this.isPublishingAudio
      console.log(message)
      this.engine.nertcPrint(message)
      this.isPublishingAudio = !this.isPublishingAudio
      this.engine.enableMediaPub(this.isPublishingAudio)
    },
    muteLocalAudio() {
      let message = 'muteLocalAudio 是否停止发送语音: ' + this.isMuteAudio
      console.log(message)
      this.engine.nertcPrint(message)
      this.isMuteAudio = !this.isMuteAudio
      this.engine.muteLocalAudio(this.isMuteAudio)
    },
    setRecordDeviceMute() {
      let message = 'setRecordDeviceMute 停止采集语音: ' + this.isStopAudio
      console.log(message)
      this.engine.nertcPrint(message)
      this.isStopAudio = !this.isStopAudio
      this.engine.setRecordDeviceMute(this.isStopAudio)
    },
    openTheMic() {
      let message = 'openTheMic 停止音频: ' + this.isOpenAudio
      console.log(message)
      this.engine.nertcPrint(message)
      this.isOpenAudio = !this.isOpenAudio
      this.engine.enableLocalAudio(this.isOpenAudio)
    },
    openTheCamera() {
      if (this.isPublishingStream) {
        return this.closeTheCamera()
      }
      console.log('打开摄像头 openTheCamera')
      this.engine.nertcPrint('打开摄像头 openTheCamera')
      this.engine.enableLocalVideo({
        enable: true, //true表示设置启动摄像头，false表示关闭摄像头
        videoStreamType: 0, //0表示视频，1表示屏幕共享
      })
      this.isPublishingStream = true
    },
    closeTheCamera() {
      console.log('关闭摄像头 closeTheCamera')
      this.engine.nertcPrint('关闭摄像头 closeTheCamera')
      this.engine.enableLocalVideo({
        enable: false, //true表示设置启动摄像头，false表示关闭摄像头
        videoStreamType: 0, //0表示视频，1表示屏幕共享
      })
      this.isPublishingStream = false
    },
    switchCamera() {
      let message = '切换摄像头 switchCamera'
      if (!this.isPublishingStream && !this.isPreviewVideo) {
        console.log('切换摄像头：当前没有启动摄像头')
        this.engine.nertcPrint('切换摄像头 当前没有启动摄像头')
        //return
      }
      console.log(message)
      this.engine.nertcPrint(message)
      this.engine.switchCamera()
    },
    muteLocalVideo() {
      let message = 'muteLocalVideo muteVideo: ' + this.isMuteVideo
      console.log(message)
      this.engine.nertcPrint(message)
      this.isMuteVideo = !this.isMuteVideo
      this.engine.muteLocalVideo(this.isMuteVideo)
    },

    setSpeakerphoneOn() {
      let message = '切换扬声器 setSpeakerphoneOn: ' + this.isSpeakerphoneOn
      console.log(message)
      this.engine.nertcPrint(message)
      this.isSpeakerphoneOn = !this.isSpeakerphoneOn
      this.engine.setSpeakerphoneOn(this.isSpeakerphoneOn)
    },
    // 预览视频
    startPreviewVideo(channel) {
      if (this.isLogin) {
        //alert('预览相关操作只能在加入房间之前执行')
        return
      }
      if (this.isPreviewVideo) {
        return this.stopPreviewVideo()
      }
      console.log('开启预览 startPreview')
      this.engine.nertcPrint('开启预览 startPreview')
      //加入房间之前
      this.engine.startPreview(NERtcVideoStreamType.MAIN)
      this.isPreviewVideo = true
    },
    // 停止预览视频
    stopPreviewVideo() {
      if (this.isLogin) {
        //alert('预览相关操作只能在加入房间之前执行')
        return
      }
      console.log('停止预览 stopPreview')
      this.engine.nertcPrint('停止预览 stopPreview')
      //加入房间之前
      this.engine.stopPreview(NERtcVideoStreamType.MAIN)
      this.isPreviewVideo = false
    },
    //拉流
    subscribeRemoteVideo(remoteUserID, remoteUserStringID) {
      let messge = `开始拉流: remoteUserID=${remoteUserID}, remoteUserStringID=${remoteUserStringID}, 设置对端视频画布`
      console.log(messge)
      this.engine.nertcPrint(messge)
      const config = Object.assign(keycenter.getRemoteVideoCanvasConfig(), {
        userID: remoteUserID,
        userStringID: remoteUserStringID,
      })
      console.log('setupRemoteVideoCanvas config: ', JSON.stringify(config))
      messge = `subscribeRemoteVideo: ${remoteUserID}, 订阅对端视频`
      console.log(messge)
      this.engine.nertcPrint(messge)
      this.engine.subscribeRemoteVideo({
        userID: remoteUserID,
        userStringID: remoteUserStringID,
        streamType: 0, //0表示大流，1表示小流
        subscribe: true, //true表示订阅，false表示取消订阅
      })
    },
    async subscribeRemoteSubStreamVideo(remoteUserID, remoteUserStringID) {
      let messge = `开始拉流: remoteUserID=${remoteUserID}, remoteUserStringID=${remoteUserStringID}, 设置对端屏幕共享的画布`
      console.log(messge)
      this.engine.nertcPrint(messge)
      const config = Object.assign(
        keycenter.getRemoteSubStreamVideoCanvasConfig(),
        {
          userID: remoteUserID,
          userStringID: remoteUserStringID,
        }
      )
      console.log(
        'subscribeRemoteSubStreamVideo config: ',
        JSON.stringify(config)
      )

      messge = `subscribeRemoteSubStreamVideo: ${remoteUserStringID}, 订阅对端屏幕共享`
      console.log(messge)
      this.engine.nertcPrint(messge)
      this.engine.subscribeRemoteSubStreamVideo({
        userID: remoteUserID,
        userStringID: remoteUserStringID,
        streamType: 0, //0表示大流，1表示小流
        subscribe: true, //true表示订阅，false表示取消订阅
      })
    },



    // 本地视频画面截图
    takeLocalSnapshot() {
      console.log('开始本地视频截图')
      this.engine.nertcPrint('开始本地视频截图')

      this.engine.takeLocalSnapshot(NERtcVideoStreamType.MAIN).then(result => {
        if (result.img) {
          const imgBase64 = result.img
          console.log('本地截图成功，Base64长度:', imgBase64.length)
          this.engine.nertcPrint('本地截图成功')

          // 准备上传数据
          const screenshotData = {
            accid: String(this.imOptions.account), // 本地用户ID
            userType: 'local', // 本地用户
            base64Data: imgBase64,
            captureTime: Date.now(),
            imageSize: imgBase64.length
          }

          // 添加到上传队列
          this.addToUploadQueue(screenshotData)
        } else {
          console.log('本地截图失败原因: ', result.errorCode)
          this.engine.nertcPrint('本地截图失败原因: ' + result.errorCode)
        }
      }).catch(error => {
        console.error('本地截图异常:', error)
        this.engine.nertcPrint('本地截图异常: ' + error)
      })
    },


  },
}
</script>

<style>
/* 页面容器 */
.call-page {
  flex: 1;
  background-color: #121212;
  display: flex;
  flex-direction: column;
}

/* 顶部状态栏 */
.call-status {
  color: #ffffff;
  font-size: 34rpx;
  font-weight: bold;
}

.call-duration {
  color: #ffffff;
  font-size: 30rpx;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

/* 视频容器 - 优化四人视频通话 */
.call-video-container {
  position: relative;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 10rpx;
  padding-bottom: 150rpx; /* 为底部工具栏留出空间 */
}

/* 视频网格 - 优化四人视频通话 */
.video-grid {
  flex: 1;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start; /* 从顶部开始排列 */
  padding: 5rpx;
  margin-top: 30px;
}

/* 视频项 - 基础样式 */
.video-item {
  position: relative;
  overflow: hidden;
  border-width: 2rpx;
  border-color: rgba(0, 255, 255, 0.1);
  border-style: solid;
  border-radius: 10rpx;
  margin: 10rpx;
}

/* 视频视图容器 */
.video-view-container {
  flex: 1;
  border-radius: 10rpx;
}

/* 视频视图 */
.video-view {
  flex: 1;
}

/* 全屏容器 */
.fullscreen-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  flex: 1;
  background-color: #000000;
  z-index: 10;
}

/* 全屏视频 */
.fullscreen-video {
  flex: 1;
  width: 750rpx;
  height: 100%;
}

/* 返回按钮 */
.fullscreen-back-btn {
  position: absolute;
  top: 40rpx;
  left: 30rpx;
  z-index: 20;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}

.back-btn-text {
  color: #ffffff;
  font-size: 28rpx;
}

/* 控制按钮区域 */
.call-controls {
  height: 140rpx;
  width: 750rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.85);
}

/* 初始化和加入房间按钮 */
.setup-buttons {
  flex-direction: row;
  justify-content: center;
  width: 750rpx;
  padding: 20rpx;
}

.setup-btn {
  width: 500rpx;
  height: 90rpx;
  border-radius: 45rpx;
  justify-content: center;
  align-items: center;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.3);
}

.join-btn {
  background-color: #4cd964;
}

.btn-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 通话控制按钮行 */
.call-action-row {
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 750rpx;
  padding: 10rpx 0;
}

/* 通话控制按钮 - 优化四人视频通话 */
.call-action-btn {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  width: 110rpx;
}

.btn-icon-container {
  width: 70rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background-color: rgba(255, 255, 255, 0.2);
  justify-content: center;
  align-items: center;
  margin-bottom: 8rpx;
}

.active-icon {
  background-color: #0a84ff;
}

.btn-icon {
  font-size: 36rpx;
  color: #ffffff;
  text-align: center;
}

.btn-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

/* 激活状态的按钮 */
.active-btn .btn-label {
  color: #0a84ff;
}

/* 挂断按钮 */
.hangup-btn {
  margin-left: 10rpx;
}

.hangup-icon {
  background-color: #ff3b30;
  transform: rotate(135deg);
}

.hangup-btn .btn-label {
  color: #ff3b30;
}

/* 禁用状态的按钮 */
.disabled-btn {
  opacity: 0.5;
}

.disabled-btn .btn-label {
  color: rgba(255, 255, 255, 0.3);
}

.disabled-btn .btn-icon-container {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 加载状态样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border-width: 6rpx;
  border-style: solid;
  border-color: #0a84ff transparent #0a84ff transparent;
  /* #ifndef APP-PLUS-NVUE */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  /* #endif */
  /* #ifdef APP-PLUS-NVUE */
  border-radius: 40rpx;
  /* #endif */
  margin-bottom: 40rpx;
}

/* #ifndef APP-PLUS-NVUE */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
/* #endif */

.loading-text {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20rpx;
  /* #ifndef APP-PLUS-NVUE */
  font-weight: 500;
  /* #endif */
  /* #ifdef APP-PLUS-NVUE */
  font-weight: bold;
  /* #endif */
}

.loading-tip {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 错误状态样式 */
.error-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.error-content {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.error-text {
  font-size: 32rpx;
  color: #ff3b30;
  text-align: center;
  margin-bottom: 60rpx;
  /* #ifndef APP-PLUS-NVUE */
  line-height: 1.5;
  /* #endif */
  /* #ifdef APP-PLUS-NVUE */
  line-height: 48rpx;
  /* #endif */
}

.error-actions {
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.retry-btn, .back-btn {
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  margin: 0 20rpx;
  justify-content: center;
  align-items: center;
  /* #ifndef APP-PLUS-NVUE */
  min-width: 160rpx;
  /* #endif */
  /* #ifdef APP-PLUS-NVUE */
  width: 160rpx;
  /* #endif */
}

.retry-btn {
  background-color: #0a84ff;
}

.back-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border-width: 2rpx;
  border-color: rgba(255, 255, 255, 0.3);
  border-style: solid;
}

.retry-text {
  color: #ffffff;
  font-size: 28rpx;
  /* #ifndef APP-PLUS-NVUE */
  font-weight: 500;
  /* #endif */
  /* #ifdef APP-PLUS-NVUE */
  font-weight: bold;
  /* #endif */
}

.back-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
}
</style>
