import request from '../utils/httpRequest';
import Config from "../utils/config.js"
import { getImToken, getPermissionKey } from './resource'

// 导出resource.js中的函数
export { getImToken, getPermissionKey }

// 获取图形验证码
export function getCaptcha() {
	return request.get({
		url: Config.APIURL + "/auth/code",
		noLoad: true, //不需要加载动画
		responseType: 'arraybuffer' // 返回二进制数据
	}).then(res => res);
}

// 验证图形验证码
export function verifyCaptcha(data) {
	return request.post({
		url: Config.APIURL + "/auth/verify/code",
		data: data,
		noLoad: true, //不需要加载动画
	}).then(res => res);
}

// 登陆验证码
export function sendCode(data) {
	return request.get({
		url: Config.APIURL + "/resource/sms/code",
		data: data,
		noLoad: true, //不需要加载动画
	}).then(res => res);
}

// 登陆
export function appPhoneLogin(data) {
	return request.post({
		url: Config.APIURL + "/auth/app/login",
		data: data,
	}).then(res => res);
}
// 获取分公司的接口
export function companyList(data) {
	return request.get({
		url: Config.APIURL + "/system/dept/usrapi/companyList",
		data:data
	}).then(res => res);
}

// 更新用户信息
export function updateUser(data) {
	return request.post({
		url: Config.APIURL + "/system/appUser/usrapi/updateUser",
		data
	}).then(res => res);
}
// 同步好友状态
export function geAscFriendStatus() {
	return request.get({
		url: Config.APIURL + "/system/appUser/usrapi/geAscFriendStatus",
		noLoad: true,
	}).then(res => res);
}

// 检查版本更新
export function checkAppVersion(data) {
	// return request.get({
	// 	url: Config.APIURL + "/system/app/version/check",
	// 	data: data,
	// 	noLoad: true,
	// }).then(res => res);
}

// 获取应用信息
export function getAppInfo() {
	// return request.get({
	// 	url: Config.APIURL + "/system/app/info",
	// 	noLoad: true,
	// }).then(res => res);
}
// 发送消息日志
export function chatLog(data) {
	return request.post({
		url: Config.APIURL + "/xchat/chatLog/usrapi/add",
		noLoad: true,
		data
	}).then(res => res);
}
// 更新消息状态日志
export function updateImStatus(data) {
	return request.post({
		url: Config.APIURL + "/system/chatLog/usrapi/edit",
		noLoad: true,
		data
	}).then(res => res);
}
// 添加亲属与长者绑定
export function addUserElder(data) {
	// 打印请求URL和数据，帮助调试
	// 尝试不同的URL格式
	const url = `${Config.APIURL}/xchat/userElderOrNurse/usrapi/addAppUserElder`;
	console.log('请求URL:', url);
	console.log('请求数据:', data);

	return request.post({
		url: url,
		noLoad: true,
		data
	}).then(res => {
		// 成功响应处理
		console.log('请求成功响应:', res);
		return res;
	}).catch(err => {
		// 错误处理
		console.error('请求错误:', err);
		throw err;
	});
}
// 获取护理任务列表
export function careTaskRecordList() {
	return request.post({
		url: Config.APIURL + '/care/taskRecord/usrapi/list',
	}).then(res => res);
}
// 添加用户与护理员绑定
export function addUserNurse(data) {
	return request.post({
		url: Config.APIURL + "/xchat/userElderOrNurse/usrapi/addAppUserNurse",
		noLoad: true,
		data
	}).then(res => res);
}

// 上传通话截图
export function uploadCallScreenshot(data) {
	console.log('上传通话截图', data)
	// return request.post({
	// 	url: Config.APIURL + "/xchat/callScreenshot/usrapi/upload",
	// 	data: data,
	// 	noLoad: true, // 不显示加载动画，避免影响通话体验
	// 	contentType: "application/json"
	// }).then(res => res);
}

// 获取任务详情- 钟永鑫
export function careTaskRecord(data) {
	return request.get({
		url: Config.APIURL + `/care/taskRecord/usrapi/${data.id}`,
		noLoad: true,
	}).then(res => res);
}
// 长者照护时间线- 钟永鑫
export function careTaskRecorEdlder(data) {
	return request.post({
		url: Config.APIURL + `/care/taskRecord/usrapi/list`,
	}).then(res => res);
}
// 开始执行照顾任务- 钟永鑫
export function careTaskRecordstartExecuting(data) {
	return request.post({
		url: Config.APIURL + `/care/taskRecord/usrapi/start/executing/${data.id}`,
		noLoad: true,
	}).then(res => res);
}
// 根据用户ID获取用户的长辈信息列表- 肖剑峰
export function getUserElderList() {
	return request.post({
		url: Config.APIURL + `/care/taskRecord/usrapi/list`,
	}).then(res => res);
}
// 获取用户信息- 肖剑峰
export function getImUserInfo(accId) {
	return request.get({
		url: Config.APIURL + `/system/imUser/usrapi/getImUserInfo/${accId}`,
		noLoad: true,
	}).then(res => res);
}

// im云信刷新token
export function refreshToken(accId) {
	return request.post({
		url: Config.APIURL + `/xchat/imUser/usrapi/refreshToken/${accId}`,
	})
}
// app退出登录
export function appLogout () {
	return request.post({
		url: Config.APIURL + `/auth/app/logout`,
		noLoad: true
	})
}
